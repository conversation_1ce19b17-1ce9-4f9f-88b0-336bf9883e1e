#include "wifi_dpp.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_dpp.h"
#include "esp_netif.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "nvs_flash.h"
#include "qrcode.h"
#include <string.h>

static const char *TAG = "wifi_dpp";

// DPP事件位定义
#define DPP_CONNECTED_BIT    BIT0
#define DPP_CONNECT_FAIL_BIT BIT1
#define DPP_AUTH_FAIL_BIT    BIT2
#define DPP_TIMEOUT_BIT      BIT3

// 最大重试次数
#define WIFI_MAX_RETRY_NUM   3
#define DPP_MAX_RETRY_NUM    5

// 私钥格式前缀和后缀
#define CURVE_SEC256R1_PKEY_HEX_DIGITS 64
#define DPP_KEY_PREFIX "30310201010420"
#define DPP_KEY_POSTFIX "a00a06082a8648ce3d030107"

// 全局变量
static wifi_dpp_config_t s_dpp_config = {0};
static wifi_dpp_state_t s_dpp_state = WIFI_DPP_STATE_IDLE;
static EventGroupHandle_t s_dpp_event_group = NULL;
static wifi_config_t s_wifi_config = {0};
static int s_wifi_retry_num = 0;
static int s_dpp_retry_num = 0;
static bool s_is_initialized = false;
static bool s_is_connected = false;
static esp_netif_t *s_sta_netif = NULL;

// 内部函数声明
static void wifi_event_handler(void *arg, esp_event_base_t event_base, int32_t event_id, void *event_data);
static void dpp_enrollee_event_cb(esp_supp_dpp_event_t event, void *data);
static esp_err_t dpp_enrollee_bootstrap(void);
static void dpp_timeout_task(void *arg);

esp_err_t wifi_dpp_init(const wifi_dpp_config_t *config)
{
    if (s_is_initialized) {
        ESP_LOGW(TAG, "DPP already initialized");
        return ESP_OK;
    }

    if (!config) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return ESP_ERR_INVALID_ARG;
    }

    // 复制配置
    memcpy(&s_dpp_config, config, sizeof(wifi_dpp_config_t));
    
    // 创建事件组
    s_dpp_event_group = xEventGroupCreate();
    if (!s_dpp_event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_ERR_NO_MEM;
    }

    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // 创建默认WiFi STA接口
    s_sta_netif = esp_netif_create_default_wifi_sta();
    if (!s_sta_netif) {
        ESP_LOGE(TAG, "Failed to create default WiFi STA interface");
        vEventGroupDelete(s_dpp_event_group);
        return ESP_FAIL;
    }

    // 注册WiFi事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));

    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));

    // 初始化DPP
    ESP_ERROR_CHECK(esp_supp_dpp_init(dpp_enrollee_event_cb));

    s_is_initialized = true;
    s_dpp_state = WIFI_DPP_STATE_IDLE;
    
    ESP_LOGI(TAG, "DPP initialized successfully");
    return ESP_OK;
}

esp_err_t wifi_dpp_start(void)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "DPP not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (s_dpp_state != WIFI_DPP_STATE_IDLE) {
        ESP_LOGW(TAG, "DPP already started");
        return ESP_OK;
    }

    // 重置状态
    s_wifi_retry_num = 0;
    s_dpp_retry_num = 0;
    s_is_connected = false;
    xEventGroupClearBits(s_dpp_event_group, 0xFF);

    // 生成引导信息
    esp_err_t ret = dpp_enrollee_bootstrap();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to generate DPP bootstrap: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启动WiFi
    ESP_ERROR_CHECK(esp_wifi_start());
    
    s_dpp_state = WIFI_DPP_STATE_LISTENING;
    
    // 启动超时任务
    if (s_dpp_config.timeout_ms > 0) {
        xTaskCreate(dpp_timeout_task, "dpp_timeout", 2048, NULL, 5, NULL);
    }

    ESP_LOGI(TAG, "DPP started, listening for authentication");
    return ESP_OK;
}

esp_err_t wifi_dpp_stop(void)
{
    if (!s_is_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (s_dpp_state == WIFI_DPP_STATE_IDLE) {
        return ESP_OK;
    }

    // 停止WiFi
    esp_wifi_stop();
    
    // 停止DPP监听
    esp_supp_dpp_stop_listen();
    
    s_dpp_state = WIFI_DPP_STATE_IDLE;
    s_is_connected = false;
    
    ESP_LOGI(TAG, "DPP stopped");
    return ESP_OK;
}

esp_err_t wifi_dpp_deinit(void)
{
    if (!s_is_initialized) {
        return ESP_OK;
    }

    // 停止DPP
    wifi_dpp_stop();

    // 反初始化DPP
    esp_supp_dpp_deinit();

    // 注销事件处理器
    esp_event_handler_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler);
    esp_event_handler_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler);

    // 反初始化WiFi
    esp_wifi_deinit();

    // 删除事件组
    if (s_dpp_event_group) {
        vEventGroupDelete(s_dpp_event_group);
        s_dpp_event_group = NULL;
    }

    s_is_initialized = false;
    s_dpp_state = WIFI_DPP_STATE_IDLE;
    
    ESP_LOGI(TAG, "DPP deinitialized");
    return ESP_OK;
}

wifi_dpp_state_t wifi_dpp_get_state(void)
{
    return s_dpp_state;
}

bool wifi_dpp_is_connected(void)
{
    return s_is_connected;
}

// WiFi事件处理器
static void wifi_event_handler(void *arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        ESP_ERROR_CHECK(esp_supp_dpp_start_listen());
        ESP_LOGI(TAG, "Started listening for DPP Authentication");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_wifi_retry_num < WIFI_MAX_RETRY_NUM) {
            esp_wifi_connect();
            s_wifi_retry_num++;
            ESP_LOGI(TAG, "Retry to connect to the AP (%d/%d)", s_wifi_retry_num, WIFI_MAX_RETRY_NUM);
        } else {
            xEventGroupSetBits(s_dpp_event_group, DPP_CONNECT_FAIL_BIT);
            s_dpp_state = WIFI_DPP_STATE_FAILED;
        }
        s_is_connected = false;
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_CONNECTED) {
        ESP_LOGI(TAG, "Successfully connected to AP: %s", s_wifi_config.sta.ssid);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t *event = (ip_event_got_ip_t *) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        s_wifi_retry_num = 0;
        s_is_connected = true;
        s_dpp_state = WIFI_DPP_STATE_CONNECTED;
        xEventGroupSetBits(s_dpp_event_group, DPP_CONNECTED_BIT);
        
        // 触发连接成功事件
        if (s_dpp_config.event_cb) {
            wifi_dpp_event_data_t event_data = {
                .event = WIFI_DPP_EVENT_CONNECTED,
                .data.connected = {
                    .ip = {
                        (event->ip_info.ip.addr >> 0) & 0xFF,
                        (event->ip_info.ip.addr >> 8) & 0xFF,
                        (event->ip_info.ip.addr >> 16) & 0xFF,
                        (event->ip_info.ip.addr >> 24) & 0xFF
                    }
                }
            };
            strncpy(event_data.data.connected.ssid, (char*)s_wifi_config.sta.ssid, sizeof(event_data.data.connected.ssid) - 1);
            s_dpp_config.event_cb(&event_data);
        }
    }
}

// DPP事件回调函数
void dpp_enrollee_event_cb(esp_supp_dpp_event_t event, void *data)
{
    switch (event) {
    case ESP_SUPP_DPP_URI_READY:
        if (data != NULL) {
            ESP_LOGI(TAG, "DPP QR Code ready");

            // 显示二维码
            esp_qrcode_config_t cfg = ESP_QRCODE_CONFIG_DEFAULT();
            ESP_LOGI(TAG, "Scan below QR Code to configure the enrollee:");
            esp_qrcode_generate(&cfg, (const char *)data);

            // 触发二维码就绪事件
            if (s_dpp_config.event_cb) {
                wifi_dpp_event_data_t event_data = {
                    .event = WIFI_DPP_EVENT_QR_CODE_READY,
                    .data.qr_ready.qr_code = (char*)data
                };
                s_dpp_config.event_cb(&event_data);
            }
        }
        break;

    case ESP_SUPP_DPP_CFG_RECVD:
        ESP_LOGI(TAG, "DPP configuration received");
        s_dpp_state = WIFI_DPP_STATE_CONFIGURING;

        memcpy(&s_wifi_config, data, sizeof(s_wifi_config));
        s_wifi_retry_num = 0;
        esp_wifi_set_config(ESP_IF_WIFI_STA, &s_wifi_config);
        esp_wifi_connect();

        // 触发配置接收事件
        if (s_dpp_config.event_cb) {
            wifi_dpp_event_data_t event_data = {
                .event = WIFI_DPP_EVENT_CONFIG_RECEIVED,
                .data.config_received.config = s_wifi_config
            };
            s_dpp_config.event_cb(&event_data);
        }
        break;

    case ESP_SUPP_DPP_FAIL:
        ESP_LOGW(TAG, "DPP Authentication failed (Reason: %s)", esp_err_to_name((int)data));

        if (s_dpp_retry_num < DPP_MAX_RETRY_NUM) {
            ESP_LOGI(TAG, "DPP retry (%d/%d)", s_dpp_retry_num + 1, DPP_MAX_RETRY_NUM);
            ESP_ERROR_CHECK(esp_supp_dpp_start_listen());
            s_dpp_retry_num++;
        } else {
            s_dpp_state = WIFI_DPP_STATE_FAILED;
            xEventGroupSetBits(s_dpp_event_group, DPP_AUTH_FAIL_BIT);

            // 触发认证失败事件
            if (s_dpp_config.event_cb) {
                wifi_dpp_event_data_t event_data = {
                    .event = WIFI_DPP_EVENT_AUTH_FAILED,
                    .data.failed.reason = (esp_err_t)data
                };
                s_dpp_config.event_cb(&event_data);
            }
        }
        break;

    default:
        break;
    }
}

// 生成DPP引导信息
static esp_err_t dpp_enrollee_bootstrap(void)
{
    esp_err_t ret;
    size_t pkey_len = strlen(s_dpp_config.bootstrapping_key);
    char *key = NULL;

    if (pkey_len) {
        // 目前只支持NIST P-256曲线，添加相应的前缀/后缀
        if (pkey_len != CURVE_SEC256R1_PKEY_HEX_DIGITS) {
            ESP_LOGE(TAG, "Invalid key length! Private key needs to be 32 bytes (or 64 hex digits) long");
            return ESP_FAIL;
        }

        key = malloc(sizeof(DPP_KEY_PREFIX) + pkey_len + sizeof(DPP_KEY_POSTFIX));
        if (!key) {
            ESP_LOGE(TAG, "Failed to allocate for bootstrapping key");
            return ESP_ERR_NO_MEM;
        }
        sprintf(key, "%s%s%s", DPP_KEY_PREFIX, s_dpp_config.bootstrapping_key, DPP_KEY_POSTFIX);
    }

    // 目前只支持二维码方式
    ret = esp_supp_dpp_bootstrap_gen(
        s_dpp_config.listen_channel_list,
        DPP_BOOTSTRAP_QR_CODE,
        key,
        s_dpp_config.device_info
    );

    if (key) {
        free(key);
    }

    return ret;
}

// 超时任务
static void dpp_timeout_task(void *arg)
{
    vTaskDelay(pdMS_TO_TICKS(s_dpp_config.timeout_ms));

    if (s_dpp_state != WIFI_DPP_STATE_CONNECTED && s_dpp_state != WIFI_DPP_STATE_IDLE) {
        ESP_LOGW(TAG, "DPP timeout after %d ms", s_dpp_config.timeout_ms);
        s_dpp_state = WIFI_DPP_STATE_FAILED;
        xEventGroupSetBits(s_dpp_event_group, DPP_TIMEOUT_BIT);

        // 触发超时事件
        if (s_dpp_config.event_cb) {
            wifi_dpp_event_data_t event_data = {
                .event = WIFI_DPP_EVENT_TIMEOUT
            };
            s_dpp_config.event_cb(&event_data);
        }
    }

    vTaskDelete(NULL);
}
