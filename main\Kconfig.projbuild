menu "LinkPet Configuration"

    menu "WiFi DPP Configuration"
        
        config ESP_DPP_LISTEN_CHANNEL_LIST
            string "DPP Listen channel list"
            default "6"
            help
                DPP Bootstrapping listen channels separated by commas.
                Common channels: 1,6,11 for 2.4GHz
        
        config ESP_DPP_BOOTSTRAPPING_KEY
            string "Bootstrapping key"
            default ""
            help
                64 hex digits (or 32 bytes) of raw private key for DPP Bootstrapping.
                Leave empty to generate a random key.
        
        config ESP_DPP_DEVICE_INFO
            string "Additional Device Info"
            default "LinkPet ESP32-C6"
            help
                Additional ancillary information to be included in QR Code.
                This will be displayed to the user during configuration.
        
        config ESP_DPP_TIMEOUT_MS
            int "DPP Configuration timeout (ms)"
            default 120000
            range 30000 300000
            help
                Timeout for DPP configuration process in milliseconds.
                After this time, DPP will stop listening if no configuration is received.
    
    endmenu

endmenu
